/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2010. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON>E<PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */


STATIC_METADATA2_BEGIN(DEVICE, SCALER, SENSOR_DRVNAME_OV13B10MAIN_MIPI_RAW)
//------------------------------------------------------------------------------
//  android.scaler
//------------------------------------------------------------------------------
    //==========================================================================
    CONFIG_METADATA_BEGIN(MTK_SCALER_AVAILABLE_MAX_DIGITAL_ZOOM)//
        CONFIG_ENTRY_VALUE(4, MFLOAT)
    CONFIG_METADATA_END()

	CONFIG_METADATA_BEGIN(MTK_CONTROL_ZOOM_RATIO_RANGE)//
		CONFIG_ENTRY_VALUE(0.5, MFLOAT)
        CONFIG_ENTRY_VALUE(4, MFLOAT)
    CONFIG_METADATA_END()
    //==========================================================================
    CONFIG_METADATA_BEGIN(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS)//new hidden

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32) //13mp 4:3
                CONFIG_ENTRY_VALUE(4160, MINT32)
                CONFIG_ENTRY_VALUE(3120, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32) //13mp 16:9
                CONFIG_ENTRY_VALUE(4160, MINT32)
                CONFIG_ENTRY_VALUE(2496, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32) //1:1
                CONFIG_ENTRY_VALUE(3120, MINT32)
                CONFIG_ENTRY_VALUE(3120, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32) //8mp 16:9
                CONFIG_ENTRY_VALUE(3840, MINT32)
                CONFIG_ENTRY_VALUE(2160, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE(2560, MINT32)
                CONFIG_ENTRY_VALUE(1920, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE(2000, MINT32)
                CONFIG_ENTRY_VALUE(1200, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE(1920, MINT32)
                CONFIG_ENTRY_VALUE(1088, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE(1920, MINT32)
                CONFIG_ENTRY_VALUE(1080, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE(1600, MINT32)
                CONFIG_ENTRY_VALUE(1200, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE(1280, MINT32)
                CONFIG_ENTRY_VALUE( 720, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE( 640, MINT32)
                CONFIG_ENTRY_VALUE( 480, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT32)
                CONFIG_ENTRY_VALUE( 320, MINT32)
                CONFIG_ENTRY_VALUE( 240, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32) //13mp 4:3
                CONFIG_ENTRY_VALUE(4160, MINT32)
                CONFIG_ENTRY_VALUE(3120, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32) //13mp 16:9
                CONFIG_ENTRY_VALUE(4160, MINT32)
                CONFIG_ENTRY_VALUE(2496, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32) //1:1
                CONFIG_ENTRY_VALUE(3120, MINT32)
                CONFIG_ENTRY_VALUE(3120, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32) //8mp 16:9
                CONFIG_ENTRY_VALUE(3840, MINT32)
                CONFIG_ENTRY_VALUE(2160, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE(2560, MINT32)
                CONFIG_ENTRY_VALUE(1920, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE(2000, MINT32)
                CONFIG_ENTRY_VALUE(1200, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE(1920, MINT32)
                CONFIG_ENTRY_VALUE(1088, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE(1920, MINT32)
                CONFIG_ENTRY_VALUE(1080, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE(1600, MINT32)
                CONFIG_ENTRY_VALUE(1200, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE(1280, MINT32)
                CONFIG_ENTRY_VALUE( 720, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE( 720, MINT32)
                CONFIG_ENTRY_VALUE( 480, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE( 640, MINT32)
                CONFIG_ENTRY_VALUE( 480, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE( 352, MINT32)
                CONFIG_ENTRY_VALUE( 288, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE( 320, MINT32)
                CONFIG_ENTRY_VALUE( 240, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT32)
                CONFIG_ENTRY_VALUE( 176, MINT32)
                CONFIG_ENTRY_VALUE( 144, MINT32)
                CONFIG_ENTRY_VALUE(MTK_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT, MINT32)

    CONFIG_METADATA_END()
    //==========================================================================
    CONFIG_METADATA_BEGIN(MTK_SCALER_AVAILABLE_MIN_FRAME_DURATIONS)//new hidden

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //13mp 4:3
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(66666666, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //13mp 16:9
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(2496, MINT64)
                CONFIG_ENTRY_VALUE(66666666, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //1:1
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(50000000, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //8mp 16:9
                CONFIG_ENTRY_VALUE(3840, MINT64)
                CONFIG_ENTRY_VALUE(2160, MINT64)
                CONFIG_ENTRY_VALUE(50000000, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(2560, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(2000, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(66666666, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1088, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1080, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1600, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(66666666, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1280, MINT64)
                CONFIG_ENTRY_VALUE( 720, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE( 640, MINT64)
                CONFIG_ENTRY_VALUE( 480, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE( 320, MINT64)
                CONFIG_ENTRY_VALUE( 240, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //13mp 4:3
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(100000000, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //13mp 16:9
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(2496, MINT64)
                CONFIG_ENTRY_VALUE(50000000, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //1:1
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(50000000, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //8mp 16:9
                CONFIG_ENTRY_VALUE(3840, MINT64)
                CONFIG_ENTRY_VALUE(2160, MINT64)
                CONFIG_ENTRY_VALUE(50000000, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(2560, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(50000000, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(2000, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1088, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1080, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1600, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1280, MINT64)
                CONFIG_ENTRY_VALUE( 720, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 720, MINT64)
                CONFIG_ENTRY_VALUE( 480, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 640, MINT64)
                CONFIG_ENTRY_VALUE( 480, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 352, MINT64)
                CONFIG_ENTRY_VALUE( 288, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 320, MINT64)
                CONFIG_ENTRY_VALUE( 240, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 176, MINT64)
                CONFIG_ENTRY_VALUE( 144, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

    CONFIG_METADATA_END()
    //==========================================================================
    CONFIG_METADATA_BEGIN(MTK_SCALER_AVAILABLE_STALL_DURATIONS)//new hidden

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //13mp 4:3
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //13mp 16:9
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(2496, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //1:1
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64) //8mp 16:9
                CONFIG_ENTRY_VALUE(3840, MINT64)
                CONFIG_ENTRY_VALUE(2160, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(2560, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(2000, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1088, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1080, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1600, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE(1280, MINT64)
                CONFIG_ENTRY_VALUE( 720, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE( 640, MINT64)
                CONFIG_ENTRY_VALUE( 480, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_BLOB, MINT64)
                CONFIG_ENTRY_VALUE( 320, MINT64)
                CONFIG_ENTRY_VALUE( 240, MINT64)
                CONFIG_ENTRY_VALUE(33333333, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //13mp 4:3
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(0   , MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //13mp 16:9
                CONFIG_ENTRY_VALUE(4160, MINT64)
                CONFIG_ENTRY_VALUE(2496, MINT64)
                CONFIG_ENTRY_VALUE(0   , MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //1:1
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(3120, MINT64)
                CONFIG_ENTRY_VALUE(0   , MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64) //8mp 16:9
                CONFIG_ENTRY_VALUE(3840, MINT64)
                CONFIG_ENTRY_VALUE(2160, MINT64)
                CONFIG_ENTRY_VALUE(0   , MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(2560, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(0   , MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(2000, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(0   , MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1088, MINT64)
                CONFIG_ENTRY_VALUE(0   , MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1920, MINT64)
                CONFIG_ENTRY_VALUE(1080, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1600, MINT64)
                CONFIG_ENTRY_VALUE(1200, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE(1280, MINT64)
                CONFIG_ENTRY_VALUE( 720, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 720, MINT64)
                CONFIG_ENTRY_VALUE( 480, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 640, MINT64)
                CONFIG_ENTRY_VALUE( 480, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 352, MINT64)
                CONFIG_ENTRY_VALUE( 288, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 320, MINT64)
                CONFIG_ENTRY_VALUE( 240, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

                CONFIG_ENTRY_VALUE(HAL_PIXEL_FORMAT_YCbCr_420_888, MINT64)
                CONFIG_ENTRY_VALUE( 176, MINT64)
                CONFIG_ENTRY_VALUE( 144, MINT64)
                CONFIG_ENTRY_VALUE(   0, MINT64)

    CONFIG_METADATA_END()
    //==========================================================================
    CONFIG_METADATA_BEGIN(MTK_REPROCESS_MAX_CAPTURE_STALL)
        CONFIG_ENTRY_VALUE(3 , MINT32)
    CONFIG_METADATA_END()
    //==========================================================================
    CONFIG_METADATA_BEGIN(MTK_SCALER_CROPPING_TYPE)//new
        CONFIG_ENTRY_VALUE(MTK_SCALER_CROPPING_TYPE_CENTER_ONLY , MUINT8)
    CONFIG_METADATA_END()
    //==========================================================================
//------------------------------------------------------------------------------
//  android.jpeg
//------------------------------------------------------------------------------
    CONFIG_METADATA_BEGIN(MTK_JPEG_AVAILABLE_THUMBNAIL_SIZES)
        CONFIG_ENTRY_VALUE(MSize(0,   0), MSize)
        CONFIG_ENTRY_VALUE(MSize(160, 96), MSize)
        CONFIG_ENTRY_VALUE(MSize(192, 108), MSize)
        CONFIG_ENTRY_VALUE(MSize(176, 128), MSize)
        CONFIG_ENTRY_VALUE(MSize(192, 144), MSize)
    CONFIG_METADATA_END()
    //==========================================================================
    CONFIG_METADATA_BEGIN(MTK_JPEG_MAX_SIZE)//
        CONFIG_ENTRY_VALUE(12979200, MINT32) //4160*3120*1
    CONFIG_METADATA_END()
    //==========================================================================
//------------------------------------------------------------------------------
STATIC_METADATA_END()
